<div class="container">
  <mat-card>
    <mat-card-title> <PERSON><PERSON> U<PERSON>! </mat-card-title>

    <mat-card-content>
      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>Nome</mat-label>
          <input matInput formControlName="name" type="text" required />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" type="email" required />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Senha</mat-label>
          <input
            matInput
            formControlName="password"
            [type]="hidePassword ? 'password' : 'text'"
            autocomplete="current-password"
            required
          />
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="!hidePassword"
          >
            <mat-icon>{{ hidePassword ? 'visibility' : 'visibility_off' }}</mat-icon>
          </button>
        </mat-form-field>

        <button
          mat-raised-button
          color="primary"
          class="form-button"
          type="submit"
        >
          Cadastrar
        </button>

        <a
          mat-stroked-button
          color="link"
          class="form-button"
          routerLink="/login"
          >Voltar
        </a>
      </form>
    </mat-card-content>
  </mat-card>
</div>
