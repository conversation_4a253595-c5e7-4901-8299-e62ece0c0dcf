.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

mat-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 350px;
  min-width: 300px;
  padding: 20px 10px;
}

mat-chip {
  margin: 5px;
}

.titulo{
  font-size: 20px;
  font-weight: bold;
}

.subTitulo{
  color: rgba(0, 0, 0, 0.54);
}

.full_width {
  width: 100%;
}

.selected {
  background-color: #3f51b5 !important;
}

.selectedText{
  color: white;
}

.mtop{
  margin-top: 50px;
}

.spinerCenter{
  text-align: center;
  margin-top: 60px;
}

/* Media Queries para Responsividade */
@media (max-width: 768px) {
  .allGrid {
    padding: 0 16px;
    /* Reduz o padding para tablet */
  }

  .titulo {
    font-size: 18px;
  }

  mat-chip {
    margin: 3px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .allGrid {
    padding: 0 12px;
    /* Padding menor para mobile */
    gap: 12px;
  }

  .m-top {
    margin-top: 12px;
  }

  .titulo {
    font-size: 16px;
    line-height: 1.3;
  }

  .subTitulo {
    font-size: 14px;
  }

  .allBetween {
    flex-direction: column;
    gap: 6px;
  }

  .mtop {
    margin-top: 30px;
  }

  mat-chip {
    margin: 2px;
    font-size: 11px;
  }

  button.mat-raised-button {
    padding: 0 8px;
    font-size: 14px;
  }
}

.password-toggle-container {
  display: flex;
  align-items: center;
  margin-right: -8px;
  /* Ajuste para alinhamento */
}

/* Garante que o botão não perca visibilidade */
.mat-form-field-appearance-outline .mat-form-field-suffix {
  opacity: 1 !important;
}

/* Estilo para o botão de visualização */
.mat-form-field .mat-icon-button {
  margin-left: 4px;
}

.mat-form-field .mat-icon {
  color: rgba(0, 0, 0, 0.6);
  font-size: 20px;
}

/* Mantenha todas as outras regras e adicione estas: */

.password-toggle-container {
  display: flex;
  align-items: center;
  margin-left: 8px;
  /* Espaçamento adequado */
}

/* Sobrescreva o comportamento padrão do Angular Material */
.mat-form-field-appearance-outline .mat-form-field-suffix {
  opacity: 1 !important;
  visibility: visible !important;
  transition: none !important;
}

/* Estilo do ícone */
.password-toggle-icon {
  color: rgba(0, 0, 0, 0.54);
  font-size: 20px;
  cursor: pointer;
}

/* Ajuste para mobile */
@media (max-width: 480px) {
  .password-toggle-icon {
    font-size: 18px;
  }
}
